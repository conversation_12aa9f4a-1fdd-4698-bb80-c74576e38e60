#!/usr/bin/env python3
"""
Universal MCP HTTP Bridge
通用MCP HTTP桥接器 - 支持所有AI工具
"""

import asyncio
import json
import sys
import os
import argparse
import yaml
from typing import Dict, Any, List, Optional
from pathlib import Path
import importlib.util

try:
    import httpx
except ImportError:
    print("正在安装依赖包...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "httpx", "pyyaml"])
    import httpx


class UniversalMCPBridge:
    """通用MCP HTTP桥接器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = self.load_config(config_path)
        self.client = None
        self.tool_mappings = {}
        self.initialize()
    
    def load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """加载配置文件"""
        # 默认配置
        default_config = {
            "server": {
                "url": os.getenv('MCP_SERVER_URL', 'https://localhost:8000'),
                "timeout": 30.0,
                "retry_count": 3
            },
            "auth": {
                "type": "bearer",  # bearer, api_key, basic, none
                "token": os.getenv('MCP_TOKEN', ''),
                "header_name": "Authorization",
                "prefix": "Bearer"
            },
            "tools": {},
            "logging": {
                "level": "INFO",
                "file": None
            }
        }
        
        # 尝试加载配置文件
        config_locations = [
            config_path,
            os.getenv('MCP_CONFIG'),
            './mcp_bridge_config.yaml',
            './mcp_bridge_config.json',
            os.path.expanduser('~/.mcp/bridge_config.yaml'),
            os.path.expanduser('~/.config/mcp/bridge_config.yaml')
        ]
        
        for location in config_locations:
            if location and Path(location).exists():
                try:
                    with open(location, 'r', encoding='utf-8') as f:
                        if location.endswith('.yaml') or location.endswith('.yml'):
                            file_config = yaml.safe_load(f)
                        else:
                            file_config = json.load(f)
                    
                    # 深度合并配置
                    default_config.update(file_config)
                    break
                except Exception as e:
                    print(f"警告: 无法加载配置文件 {location}: {e}", file=sys.stderr)
        
        return default_config
    
    def initialize(self):
        """初始化桥接器"""
        # 设置HTTP客户端
        headers = {'Content-Type': 'application/json'}
        
        # 配置认证
        auth_config = self.config.get('auth', {})
        if auth_config.get('type') == 'bearer' and auth_config.get('token'):
            headers[auth_config.get('header_name', 'Authorization')] = \
                f"{auth_config.get('prefix', 'Bearer')} {auth_config['token']}"
        elif auth_config.get('type') == 'api_key' and auth_config.get('token'):
            headers[auth_config.get('header_name', 'X-API-Key')] = auth_config['token']
        
        self.client = httpx.AsyncClient(
            timeout=self.config['server']['timeout'],
            headers=headers
        )
        
        # 加载工具映射
        self.load_tool_mappings()
    
    def load_tool_mappings(self):
        """加载工具映射配置"""
        tools_config = self.config.get('tools', {})
        
        # 如果配置了工具映射，使用配置
        if tools_config:
            self.tool_mappings = tools_config
        else:
            # 否则尝试从服务器获取
            self.load_tools_from_server()
    
    async def load_tools_from_server(self):
        """从服务器动态加载工具映射"""
        try:
            server_url = self.config['server']['url']
            response = await self.client.get(f"{server_url}/api/v1/tools/mappings")
            if response.status_code == 200:
                self.tool_mappings = response.json()
            else:
                # 使用默认映射
                self.tool_mappings = self.get_default_mappings()
        except Exception:
            self.tool_mappings = self.get_default_mappings()
    
    def get_default_mappings(self) -> Dict[str, Dict[str, Any]]:
        """获取默认工具映射"""
        return {
            'initialize_project_constitution': {
                'endpoint': '/api/v1/projects/init',
                'method': 'POST',
                'description': '初始化项目宪法，创建项目的核心规约文档',
                'parameters': {
                    'project_name': {'type': 'string', 'description': '项目名称', 'required': True},
                    'project_description': {'type': 'string', 'description': '项目描述', 'required': True},
                    'tech_stack': {'type': 'string', 'description': '技术栈描述', 'required': True},
                    'team_size': {'type': 'integer', 'description': '团队规模', 'required': True},
                    'team_experience': {'type': 'string', 'description': '团队经验水平', 'required': True},
                    'performance_requirements': {'type': 'string', 'description': '性能需求', 'required': True},
                    'security_requirements': {'type': 'string', 'description': '安全需求', 'required': True},
                    'design_principles': {'type': 'string', 'description': '设计原则', 'required': True},
                    'constraints': {'type': 'string', 'description': '约束条件', 'required': True}
                }
            },
            'generate_feature_spec': {
                'endpoint': '/api/v1/features/generate',
                'method': 'POST',
                'description': '生成功能规约，包括需求、设计和任务规约',
                'parameters': {
                    'feature_name': {'type': 'string', 'description': '功能名称', 'required': True},
                    'feature_description': {'type': 'string', 'description': '功能描述', 'required': True},
                    'user_stories': {'type': 'string', 'description': '用户故事', 'required': True},
                    'acceptance_criteria': {'type': 'string', 'description': '验收标准', 'required': True}
                }
            },
            'get_project_context': {
                'endpoint': '/api/v1/projects/context',
                'method': 'GET',
                'description': '获取当前项目的上下文信息',
                'parameters': {}
            },
            'list_features': {
                'endpoint': '/api/v1/features',
                'method': 'GET',
                'description': '列出所有已创建的功能规约',
                'parameters': {}
            },
            'validate_code_compliance': {
                'endpoint': '/api/v1/validate/code',
                'method': 'POST',
                'description': '验证代码是否符合项目规约',
                'parameters': {
                    'file_path': {'type': 'string', 'description': '文件路径', 'required': True},
                    'code_content': {'type': 'string', 'description': '代码内容', 'required': True}
                }
            },
            'generate_tests': {
                'endpoint': '/api/v1/tests/generate',
                'method': 'POST',
                'description': '基于需求规约生成测试代码',
                'parameters': {
                    'feature_name': {'type': 'string', 'description': '功能名称', 'required': True},
                    'test_type': {'type': 'string', 'description': '测试类型', 'default': 'unit'}
                }
            },
            'sync_documentation': {
                'endpoint': '/api/v1/docs/sync',
                'method': 'POST',
                'description': '同步功能文档，确保文档与代码保持一致',
                'parameters': {
                    'feature_name': {'type': 'string', 'description': '功能名称', 'required': True}
                }
            },
            'update_project_context': {
                'endpoint': '/api/v1/projects/context',
                'method': 'PUT',
                'description': '更新项目上下文信息',
                'parameters': {
                    'field': {'type': 'string', 'description': '要更新的字段', 'required': True},
                    'value': {'type': 'string', 'description': '新值', 'required': True}
                }
            }
        }
    
    async def handle_list_tools(self):
        """返回可用工具列表"""
        tools = []
        
        for tool_name, config in self.tool_mappings.items():
            # 构建参数schema
            properties = {}
            required = []
            
            for param_name, param_config in config.get('parameters', {}).items():
                properties[param_name] = {
                    'type': param_config.get('type', 'string'),
                    'description': param_config.get('description', '')
                }
                
                if param_config.get('default') is not None:
                    properties[param_name]['default'] = param_config['default']
                
                if param_config.get('required', False):
                    required.append(param_name)
            
            tool_def = {
                'name': tool_name,
                'description': config.get('description', f'调用 {tool_name}'),
                'inputSchema': {
                    'type': 'object',
                    'properties': properties
                }
            }
            
            if required:
                tool_def['inputSchema']['required'] = required
            
            tools.append(tool_def)
        
        return {
            'jsonrpc': '2.0',
            'result': {'tools': tools}
        }
    
    async def handle_call_tool(self, tool_name: str, arguments: Dict[str, Any]):
        """调用远程工具"""
        if tool_name not in self.tool_mappings:
            return {
                'jsonrpc': '2.0',
                'error': {
                    'code': -32601,
                    'message': f'Unknown tool: {tool_name}'
                }
            }
        
        mapping = self.tool_mappings[tool_name]
        server_url = self.config['server']['url']
        endpoint = f"{server_url}{mapping['endpoint']}"
        method = mapping.get('method', 'POST')
        
        # 处理参数默认值
        for param_name, param_config in mapping.get('parameters', {}).items():
            if param_name not in arguments and 'default' in param_config:
                arguments[param_name] = param_config['default']
        
        try:
            # 发送HTTP请求
            if method.upper() == 'GET':
                response = await self.client.get(endpoint, params=arguments)
            elif method.upper() == 'POST':
                response = await self.client.post(endpoint, json=arguments)
            elif method.upper() == 'PUT':
                response = await self.client.put(endpoint, json=arguments)
            elif method.upper() == 'DELETE':
                response = await self.client.delete(endpoint, params=arguments)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            result = response.json()
            
            # 格式化响应
            if isinstance(result, dict):
                text_content = result.get('message', result.get('data', str(result)))
            else:
                text_content = str(result)
            
            return {
                'jsonrpc': '2.0',
                'result': {
                    'content': [
                        {
                            'type': 'text',
                            'text': text_content
                        }
                    ]
                }
            }
            
        except httpx.TimeoutException:
            return {
                'jsonrpc': '2.0',
                'error': {
                    'code': -32603,
                    'message': f'Request timeout for tool: {tool_name}'
                }
            }
        except httpx.HTTPStatusError as e:
            return {
                'jsonrpc': '2.0',
                'error': {
                    'code': -32603,
                    'message': f'HTTP {e.response.status_code}: {e.response.text}'
                }
            }
        except Exception as e:
            return {
                'jsonrpc': '2.0',
                'error': {
                    'code': -32603,
                    'message': f'Tool execution failed: {str(e)}'
                }
            }
    
    async def handle_initialize(self, params: Dict[str, Any]):
        """处理初始化请求"""
        return {
            'jsonrpc': '2.0',
            'result': {
                'protocolVersion': '2024-11-05',
                'capabilities': {
                    'tools': {}
                },
                'serverInfo': {
                    'name': 'Universal MCP HTTP Bridge',
                    'version': '1.0.0'
                }
            }
        }
    
    async def run(self):
        """运行MCP服务器"""
        while True:
            try:
                line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
                if not line:
                    break
                
                line = line.strip()
                if not line:
                    continue
                    
                request = json.loads(line)
                method = request.get('method')
                params = request.get('params', {})
                
                # 处理不同的MCP方法
                if method == 'initialize':
                    response = await self.handle_initialize(params)
                elif method == 'tools/list':
                    response = await self.handle_list_tools()
                elif method == 'tools/call':
                    tool_name = params.get('name')
                    arguments = params.get('arguments', {})
                    response = await self.handle_call_tool(tool_name, arguments)
                else:
                    response = {
                        'jsonrpc': '2.0',
                        'error': {
                            'code': -32601,
                            'message': f'Method not found: {method}'
                        }
                    }
                
                # 添加请求ID
                if 'id' in request:
                    response['id'] = request['id']
                
                print(json.dumps(response, ensure_ascii=False), flush=True)
                
            except json.JSONDecodeError as e:
                error_response = {
                    'jsonrpc': '2.0',
                    'error': {
                        'code': -32700,
                        'message': f'Parse error: {str(e)}'
                    }
                }
                print(json.dumps(error_response), flush=True)
            except Exception as e:
                error_response = {
                    'jsonrpc': '2.0',
                    'error': {
                        'code': -32603,
                        'message': f'Internal error: {str(e)}'
                    }
                }
                print(json.dumps(error_response), flush=True)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Universal MCP HTTP Bridge')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--server-url', help='服务器URL')
    parser.add_argument('--token', help='认证令牌')
    parser.add_argument('--generate-config', help='生成示例配置文件')
    
    args = parser.parse_args()
    
    # 生成配置文件
    if args.generate_config:
        generate_sample_config(args.generate_config)
        return
    
    # 从命令行参数设置环境变量
    if args.server_url:
        os.environ['MCP_SERVER_URL'] = args.server_url
    if args.token:
        os.environ['MCP_TOKEN'] = args.token
    
    async def run_bridge():
        async with UniversalMCPBridge(args.config) as bridge:
            await bridge.run()
    
    try:
        asyncio.run(run_bridge())
    except KeyboardInterrupt:
        print("\n桥接器已停止", file=sys.stderr)
    except Exception as e:
        print(f"错误: {e}", file=sys.stderr)
        sys.exit(1)


def generate_sample_config(config_path: str):
    """生成示例配置文件"""
    sample_config = {
        'server': {
            'url': 'https://your-domain.com',
            'timeout': 30.0,
            'retry_count': 3
        },
        'auth': {
            'type': 'bearer',
            'token': 'your-api-token',
            'header_name': 'Authorization',
            'prefix': 'Bearer'
        },
        'tools': {
            'example_tool': {
                'endpoint': '/api/v1/example',
                'method': 'POST',
                'description': '示例工具',
                'parameters': {
                    'param1': {
                        'type': 'string',
                        'description': '参数1',
                        'required': True
                    },
                    'param2': {
                        'type': 'integer',
                        'description': '参数2',
                        'default': 10
                    }
                }
            }
        },
        'logging': {
            'level': 'INFO',
            'file': None
        }
    }
    
    with open(config_path, 'w', encoding='utf-8') as f:
        if config_path.endswith('.yaml') or config_path.endswith('.yml'):
            yaml.dump(sample_config, f, default_flow_style=False, allow_unicode=True)
        else:
            json.dump(sample_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 示例配置文件已生成: {config_path}")


if __name__ == "__main__":
    main() 