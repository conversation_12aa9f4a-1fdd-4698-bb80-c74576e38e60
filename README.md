# 规约驱动开发 MCP 服务器

基于 FastMCP 实现的软件开发全生命周期治理框架，旨在解决"氛围编码"问题，确保大规模、长周期项目的代码质量、可维护性和团队一致性。

## 功能特性

### 🏗️ 项目宪法与代理引导
- **项目初始化**: 基于主控提示生成项目级"宪法"文件
- **核心规约文档**: 自动生成 `product.md`、`architecture.md`、`conventions.md`
- **上下文管理**: 持久化项目配置和约束条件

### 📋 规约驱动的特性开发
- **需求规约生成**: 将模糊需求转化为结构化的用户故事和验收标准
- **设计规约生成**: 基于项目架构生成详细的技术设计方案
- **任务规约生成**: 将设计分解为可执行的原子化任务

### 🔍 自动化测试与工作流执行
- **代码合规性验证**: 检查代码是否符合项目规约
- **自动化测试生成**: 基于需求规约生成单元测试和集成测试
- **文档同步**: 确保文档与代码保持一致

## 安装和使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动 MCP 服务器
```bash
python spec_driven_mcp.py
```

### 3. 可用工具

#### 项目初始化工具
- `initialize_project_constitution` - 初始化项目宪法
- `get_project_context` - 获取项目上下文信息
- `update_project_context` - 更新项目配置

#### 功能开发工具
- `generate_feature_spec` - 生成完整的功能规约
- `list_features` - 列出所有功能规约

#### 质量保障工具
- `validate_code_compliance` - 验证代码合规性
- `generate_tests` - 生成测试代码
- `sync_documentation` - 同步文档

## 使用流程

### 第一步：初始化项目宪法
```python
# 调用 initialize_project_constitution 工具
initialize_project_constitution(
    project_name="我的项目",
    project_description="项目描述",
    tech_stack='{"frontend": "React", "backend": "Python/FastAPI"}',
    team_size=5,
    team_experience="中级",
    performance_requirements="响应时间 < 200ms",
    security_requirements="HTTPS + JWT认证",
    design_principles="SOLID原则, 微服务架构",
    constraints="禁止使用ORM, 必须使用Repository模式"
)
```

### 第二步：生成功能规约
```python
# 调用 generate_feature_spec 工具
generate_feature_spec(
    feature_name="用户管理",
    feature_description="用户注册、登录、信息管理功能",
    user_stories="作为用户，我希望能够注册账号\n作为用户，我希望能够登录系统",
    acceptance_criteria="注册成功后跳转到登录页\n登录成功后跳转到主页"
)
```

### 第三步：验证和测试
```python
# 验证代码合规性
validate_code_compliance("user_service.py", code_content)

# 生成测试代码
generate_tests("用户管理", "unit")

# 同步文档
sync_documentation("用户管理")
```

## 项目结构

```
.project-spec/
├── project_context.json     # 项目上下文配置
├── product.md              # 产品规约
├── architecture.md         # 架构规约
├── conventions.md          # 编码规约
└── features/               # 功能规约目录
    └── user_management/    # 具体功能目录
        ├── requirements.md # 需求规约
        ├── design.md      # 设计规约
        ├── tasks.md       # 任务规约
        ├── test_user_management.py  # 测试文件
        └── README.md      # 功能文档
```

## 核心理念

1. **策略定义**: 通过项目宪法建立持久化的顶层规则
2. **合规规划**: 将高层级需求转化为结构化规约
3. **监控执行**: 通过自动化确保规约的落地执行

## 技术特点

- 基于 FastMCP 框架，提供标准的 MCP 接口
- 使用 Pydantic 进行数据验证和序列化
- 支持 Mermaid 图表生成
- 完整的文件系统管理
- 可扩展的工具架构

---

*此框架实现了完整的规约驱动开发治理体系，帮助团队从"氛围编码"转向"规约驱动"的标准化开发模式。*
