#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规约驱动开发 MCP 服务器
基于 FastMCP 实现的软件开发全生命周期治理框架
"""

import os
import sys
import json
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

# 确保正确的编码设置
if sys.platform.startswith('win'):
    # Windows 系统编码设置
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
    except locale.Error:
        try:
            locale.setlocale(locale.LC_ALL, 'Chinese_China.65001')
        except locale.Error:
            pass

# 设置标准输出编码
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')

from fastmcp import FastMCP
from pydantic import BaseModel


class ProjectContext(BaseModel):
    """项目上下文模型"""
    name: str
    description: str
    tech_stack: Dict[str, Any]
    team_info: Dict[str, Any]
    design_principles: List[str]
    constraints: List[str]
    created_at: str
    updated_at: str


class SpecFile(BaseModel):
    """规约文件模型"""
    type: str  # requirements, design, tasks
    feature_name: str
    content: str
    created_at: str
    updated_at: str
    version: str


# 创建 MCP 服务器
mcp = FastMCP("Spec-Driven Development Framework")

# 项目规约目录
SPEC_DIR = Path(".project-spec")
FEATURES_DIR = SPEC_DIR / "features"


def ensure_spec_directories():
    """确保规约目录存在"""
    SPEC_DIR.mkdir(exist_ok=True)
    FEATURES_DIR.mkdir(exist_ok=True)


def load_project_context() -> Optional[ProjectContext]:
    """加载项目上下文"""
    context_file = SPEC_DIR / "project_context.json"
    if context_file.exists():
        with open(context_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return ProjectContext(**data)
    return None


def save_project_context(context: ProjectContext):
    """保存项目上下文"""
    ensure_spec_directories()
    context_file = SPEC_DIR / "project_context.json"
    with open(context_file, 'w', encoding='utf-8') as f:
        json.dump(context.model_dump(), f, ensure_ascii=False, indent=2)


@mcp.tool
def initialize_project_constitution(
    project_name: str,
    project_description: str,
    tech_stack: str,
    team_size: int,
    team_experience: str,
    performance_requirements: str,
    security_requirements: str,
    design_principles: str,
    constraints: str
) -> str:
    """
    初始化项目宪法，创建项目的核心规约文档
    
    Args:
        project_name: 项目名称
        project_description: 项目描述
        tech_stack: 技术栈描述 (JSON格式字符串)
        team_size: 团队规模
        team_experience: 团队经验水平
        performance_requirements: 性能需求
        security_requirements: 安全需求
        design_principles: 设计原则 (逗号分隔)
        constraints: 约束条件 (逗号分隔)
    """
    try:
        # 解析输入
        tech_stack_dict = json.loads(tech_stack) if tech_stack.startswith('{') else {"description": tech_stack}
        principles_list = [p.strip() for p in design_principles.split(',')]
        constraints_list = [c.strip() for c in constraints.split(',')]
        
        # 创建项目上下文
        context = ProjectContext(
            name=project_name,
            description=project_description,
            tech_stack=tech_stack_dict,
            team_info={
                "size": team_size,
                "experience": team_experience,
                "performance_requirements": performance_requirements,
                "security_requirements": security_requirements
            },
            design_principles=principles_list,
            constraints=constraints_list,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 保存项目上下文
        save_project_context(context)
        
        # 生成核心规约文档
        _generate_constitution_files(context)
        
        return f"✅ 项目宪法已成功初始化！\n\n创建的文件：\n- .project-spec/project_context.json\n- .project-spec/product.md\n- .project-spec/architecture.md\n- .project-spec/conventions.md"
        
    except Exception as e:
        return f"❌ 初始化失败: {str(e)}"


def _generate_constitution_files(context: ProjectContext):
    """生成项目宪法文件"""
    ensure_spec_directories()

    # 生成详细的产品规约文档
    _generate_product_spec(context)

    # 生成详细的架构规约文档
    _generate_architecture_spec(context)

    # 生成详细的开发规约文档
    _generate_conventions_spec(context)


def _generate_product_spec(context: ProjectContext):
    """生成详细的产品规约文档"""
    product_content = f"""# {context.name} - 产品规约

## 产品概述

### 产品愿景
{context.description}

### 核心价值主张
基于项目的技术栈和团队能力，本产品致力于提供：
- 高质量的用户体验
- 可靠的系统性能
- 安全的数据处理
- 可扩展的架构设计

## 目标用户分析

### 主要用户群体
- **开发团队**: 项目的直接开发者和维护者
- **产品经理**: 负责产品规划和需求管理
- **运维团队**: 负责系统部署和运维监控
- **最终用户**: 产品的实际使用者

### 用户需求
- 功能完整性和易用性
- 系统稳定性和可靠性
- 数据安全性和隐私保护
- 响应速度和性能优化

## 功能需求

### 核心功能模块
基于技术栈 {list(context.tech_stack.keys())} 的核心功能包括：

1. **用户管理模块**
   - 用户注册和登录
   - 权限管理和角色控制
   - 用户配置文件管理

2. **数据处理模块**
   - 数据输入和验证
   - 数据存储和检索
   - 数据分析和报告

3. **系统管理模块**
   - 系统配置管理
   - 监控和日志记录
   - 备份和恢复机制

## 非功能需求

### 性能要求
{context.team_info.get('performance_requirements', '- 系统响应时间 < 2秒\\n- 支持并发用户数 > 1000\\n- 数据处理吞吐量优化')}

### 安全要求
{context.team_info.get('security_requirements', '- 数据传输加密\\n- 用户身份验证\\n- 访问权限控制\\n- 安全审计日志')}

### 可用性要求
- 系统可用性 ≥ 99.9%
- 故障恢复时间 < 1小时
- 数据备份和灾难恢复

## 技术约束
{chr(10).join(f"- {constraint}" for constraint in context.constraints)}

## 质量标准
- 代码质量：遵循最佳实践和编码规范
- 测试覆盖：单元测试覆盖率 > 80%
- 文档完整：API文档和用户手册齐全
- 性能基准：满足预定义的性能指标

## 项目里程碑
1. **需求分析阶段** (2周)
   - 详细需求收集和分析
   - 技术方案设计和评审

2. **开发阶段** (8-12周)
   - 核心功能开发
   - 单元测试和集成测试

3. **测试阶段** (2-3周)
   - 系统测试和用户验收测试
   - 性能测试和安全测试

4. **部署阶段** (1周)
   - 生产环境部署
   - 监控和运维配置

---
*此文档由规约驱动开发系统生成，最后更新时间: {context.updated_at}*
"""

    with open(SPEC_DIR / "product.md", 'w', encoding='utf-8') as f:
        f.write(product_content)


def _generate_architecture_spec(context: ProjectContext):
    """生成详细的架构规约文档"""

    # 根据技术栈生成架构建议
    tech_stack = context.tech_stack
    frontend_techs = tech_stack.get('frontend', [])
    backend_techs = tech_stack.get('backend', [])
    database_techs = tech_stack.get('database', [])

    architecture_content = f"""# {context.name} - 架构规约

## 整体架构设计

### 架构原则
{chr(10).join(f"- **{principle}**: 确保系统的{principle.lower()}特性" for principle in context.design_principles)}

### 技术栈概览
```json
{json.dumps(context.tech_stack, ensure_ascii=False, indent=2)}
```

## 系统架构

### 分层架构设计
```mermaid
graph TB
    subgraph "前端层"
        A[用户界面] --> B[前端路由]
        B --> C[状态管理]
    end

    subgraph "API层"
        D[API网关] --> E[认证授权]
        E --> F[业务路由]
    end

    subgraph "业务层"
        G[业务逻辑] --> H[数据验证]
        H --> I[业务规则]
    end

    subgraph "数据层"
        J[数据访问] --> K[数据库]
        J --> L[缓存层]
    end

    C --> D
    F --> G
    I --> J
```

### 核心模块设计

#### 1. 前端架构
{"基于 " + ", ".join(frontend_techs) + " 的前端架构：" if frontend_techs else "前端架构设计："}
- **组件化设计**: 可复用的UI组件库
- **状态管理**: 集中式状态管理方案
- **路由管理**: 单页应用路由配置
- **API集成**: 统一的API调用封装

#### 2. 后端架构
{"基于 " + ", ".join(backend_techs) + " 的后端架构：" if backend_techs else "后端架构设计："}
- **微服务设计**: 按业务域拆分服务
- **API设计**: RESTful API规范
- **中间件**: 认证、日志、错误处理
- **异步处理**: 消息队列和任务调度

#### 3. 数据架构
{"基于 " + ", ".join(database_techs) + " 的数据架构：" if database_techs else "数据架构设计："}
- **数据建模**: 规范化的数据模型设计
- **数据访问**: ORM/ODM数据访问层
- **数据缓存**: 多级缓存策略
- **数据备份**: 自动化备份和恢复

## 部署架构

### 开发环境
- 本地开发环境配置
- 开发数据库和测试数据
- 热重载和调试工具

### 测试环境
- 持续集成/持续部署
- 自动化测试执行
- 测试数据管理

### 生产环境
- 负载均衡和高可用
- 监控和告警系统
- 日志收集和分析

## 安全架构

### 认证和授权
- 用户身份认证机制
- 基于角色的访问控制
- API安全和令牌管理

### 数据安全
- 数据加密存储和传输
- 敏感数据脱敏处理
- 数据访问审计日志

## 性能优化

### 前端性能
- 代码分割和懒加载
- 静态资源优化
- 浏览器缓存策略

### 后端性能
- 数据库查询优化
- 缓存策略设计
- 异步处理优化

## 约束条件
{chr(10).join(f"- **{constraint}**: 需要在架构设计中特别考虑" for constraint in context.constraints)}

## 团队协作

### 开发团队结构
- 团队规模: {context.team_info['size']} 人
- 经验水平: {context.team_info['experience']}
- 技能分工和协作模式

### 开发流程
- 敏捷开发方法论
- 代码审查和质量控制
- 版本发布和部署流程

---
*此文档由规约驱动开发系统生成，最后更新时间: {context.updated_at}*
"""

    with open(SPEC_DIR / "architecture.md", 'w', encoding='utf-8') as f:
        f.write(architecture_content)


def _generate_conventions_spec(context: ProjectContext):
    """生成详细的开发规约文档"""

    # 根据技术栈生成相应的编码规范
    tech_stack = context.tech_stack

    conventions_content = f"""# {context.name} - 开发规约

## 编码规范

### 通用编码原则
- **可读性优先**: 代码应该易于理解和维护
- **一致性**: 整个项目保持统一的编码风格
- **简洁性**: 避免过度复杂的设计和实现
- **可测试性**: 代码应该易于编写单元测试

### 语言特定规范

{_generate_language_conventions(tech_stack)}

## 项目结构规范

### 目录结构
```
{context.name.lower().replace(' ', '-')}/
├── src/                    # 源代码目录
│   ├── components/         # 可复用组件
│   ├── services/          # 业务服务层
│   ├── utils/             # 工具函数
│   ├── types/             # 类型定义
│   └── tests/             # 测试文件
├── docs/                  # 项目文档
├── config/                # 配置文件
├── scripts/               # 构建和部署脚本
└── .project-spec/         # 项目规约文档
```

### 文件命名规范
- 使用小写字母和连字符分隔
- 组件文件使用 PascalCase
- 配置文件使用 kebab-case
- 测试文件添加 .test 或 .spec 后缀

## API设计规范

### RESTful API设计
- 使用标准HTTP方法 (GET, POST, PUT, DELETE)
- 资源命名使用复数形式
- 统一的错误响应格式
- 版本控制策略 (v1, v2...)

### 响应格式标准
```json
{{
  "success": true,
  "data": {{}},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}}
```

### 错误处理规范
```json
{{
  "success": false,
  "error": {{
    "code": "VALIDATION_ERROR",
    "message": "输入数据验证失败",
    "details": {{}}
  }},
  "timestamp": "2024-01-01T00:00:00Z"
}}
```

## 数据库设计规范

### 命名规范
- 表名使用复数形式，小写下划线分隔
- 字段名使用小写下划线分隔
- 主键统一使用 `id`
- 外键使用 `表名_id` 格式
- 时间字段使用 `created_at`, `updated_at`

### 索引策略
- 为经常查询的字段创建索引
- 复合索引考虑字段顺序
- 避免过多索引影响写入性能

## 测试规范

### 测试分类
- **单元测试**: 测试单个函数或方法
- **集成测试**: 测试模块间的交互
- **端到端测试**: 测试完整的用户流程

### 测试覆盖率要求
- 单元测试覆盖率 ≥ 80%
- 核心业务逻辑覆盖率 ≥ 90%
- 关键API端点覆盖率 = 100%

### 测试命名规范
```
test_should_[expected_behavior]_when_[condition]
```

## 版本控制规范

### Git工作流
- 使用 Git Flow 或 GitHub Flow
- 主分支保护，通过PR合并
- 提交信息遵循约定式提交规范

### 提交信息格式
```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

类型包括：
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 代码审查规范

### 审查清单
- [ ] 代码符合编码规范
- [ ] 功能实现正确完整
- [ ] 测试覆盖充分
- [ ] 文档更新及时
- [ ] 性能影响评估
- [ ] 安全风险评估

### 审查流程
1. 开发者自测和自审
2. 同行代码审查
3. 技术负责人最终审查
4. 自动化测试通过
5. 合并到主分支

## 文档规范

### 文档类型
- **API文档**: 接口说明和示例
- **用户手册**: 功能使用指南
- **开发文档**: 技术实现说明
- **部署文档**: 环境配置和部署流程

### 文档维护
- 代码变更时同步更新文档
- 定期审查文档的准确性
- 使用自动化工具生成API文档

## 性能规范

### 响应时间要求
{context.team_info.get('performance_requirements', '- API响应时间 < 500ms\\n- 页面加载时间 < 2s\\n- 数据库查询时间 < 100ms')}

### 资源使用限制
- 内存使用优化
- CPU使用率监控
- 磁盘空间管理
- 网络带宽控制

## 安全规范

### 安全要求
{context.team_info.get('security_requirements', '- 输入数据验证和过滤\\n- SQL注入防护\\n- XSS攻击防护\\n- CSRF令牌验证')}

### 安全检查清单
- [ ] 敏感数据加密存储
- [ ] API访问权限控制
- [ ] 日志记录和审计
- [ ] 定期安全扫描

## 部署和运维规范

### 环境管理
- 开发、测试、生产环境隔离
- 配置文件环境化管理
- 密钥和敏感信息安全存储

### 监控和告警
- 应用性能监控
- 错误日志监控
- 资源使用监控
- 业务指标监控

## 项目约束条件

{chr(10).join(f"### {constraint}" for constraint in context.constraints)}

---
*此文档由规约驱动开发系统生成，最后更新时间: {context.updated_at}*
"""

    with open(SPEC_DIR / "conventions.md", 'w', encoding='utf-8') as f:
        f.write(conventions_content)


def _generate_language_conventions(tech_stack: dict) -> str:
    """根据技术栈生成语言特定的编码规范"""
    conventions = []

    # Python规范
    if any('python' in str(tech).lower() or 'fastapi' in str(tech).lower() or 'django' in str(tech).lower()
           for tech_list in tech_stack.values() for tech in tech_list):
        conventions.append("""
#### Python编码规范
- 遵循 PEP 8 标准
- 使用 Black 进行代码格式化
- 使用 type hints 进行类型注解
- 文档字符串遵循 Google 或 NumPy 风格
- 使用 f-string 进行字符串格式化

```python
def calculate_total(items: List[Item]) -> Decimal:
    \"\"\"计算商品总价

    Args:
        items: 商品列表

    Returns:
        商品总价
    \"\"\"
    return sum(item.price for item in items)
```""")

    # JavaScript/TypeScript规范
    if any('javascript' in str(tech).lower() or 'typescript' in str(tech).lower() or 'react' in str(tech).lower() or 'next' in str(tech).lower()
           for tech_list in tech_stack.values() for tech in tech_list):
        conventions.append("""
#### JavaScript/TypeScript编码规范
- 使用 ESLint 和 Prettier 进行代码检查和格式化
- 优先使用 TypeScript 进行类型安全
- 使用 const/let 替代 var
- 使用箭头函数和解构赋值
- 组件使用 PascalCase 命名

```typescript
interface UserProps {
  name: string;
  email: string;
}

const UserCard: React.FC<UserProps> = ({ name, email }) => {
  return (
    <div className="user-card">
      <h3>{name}</h3>
      <p>{email}</p>
    </div>
  );
};
```""")

    # 如果没有特定语言，返回通用规范
    if not conventions:
        conventions.append("""
#### 通用编码规范
- 使用有意义的变量和函数名
- 保持函数简短和单一职责
- 添加必要的注释和文档
- 遵循项目的代码风格指南
""")

    return "\n".join(conventions)


@mcp.tool
def generate_feature_spec(
    feature_name: str,
    feature_description: str,
    user_stories: str,
    acceptance_criteria: str
) -> str:
    """
    生成功能规约，包括需求、设计和任务规约
    
    Args:
        feature_name: 功能名称
        feature_description: 功能描述
        user_stories: 用户故事 (每行一个)
        acceptance_criteria: 验收标准 (每行一个)
    """
    try:
        context = load_project_context()
        if not context:
            return "❌ 请先初始化项目宪法"
        
        # 创建功能目录
        feature_dir = FEATURES_DIR / feature_name.lower().replace(' ', '_')
        feature_dir.mkdir(exist_ok=True)
        
        # 生成需求规约
        requirements_content = _generate_requirements_spec(
            feature_name, feature_description, user_stories, acceptance_criteria, context
        )
        
        # 生成设计规约
        design_content = _generate_design_spec(feature_name, feature_description, context)
        
        # 生成任务规约
        tasks_content = _generate_tasks_spec(feature_name, requirements_content, design_content)
        
        # 保存文件
        timestamp = datetime.now().isoformat()
        
        with open(feature_dir / "requirements.md", 'w', encoding='utf-8') as f:
            f.write(requirements_content)
        
        with open(feature_dir / "design.md", 'w', encoding='utf-8') as f:
            f.write(design_content)
        
        with open(feature_dir / "tasks.md", 'w', encoding='utf-8') as f:
            f.write(tasks_content)
        
        return f"✅ 功能规约已生成！\n\n创建的文件：\n- {feature_dir}/requirements.md\n- {feature_dir}/design.md\n- {feature_dir}/tasks.md"
        
    except Exception as e:
        return f"❌ 生成失败: {str(e)}"


def _generate_requirements_spec(feature_name: str, description: str, user_stories: str, 
                               acceptance_criteria: str, context: ProjectContext) -> str:
    """生成需求规约"""
    stories_list = [story.strip() for story in user_stories.split('\n') if story.strip()]
    criteria_list = [criteria.strip() for criteria in acceptance_criteria.split('\n') if criteria.strip()]
    
    return f"""# 需求规约 - {feature_name}

## 功能概述
{description}

## 用户故事
{chr(10).join(f"{i+1}. {story}" for i, story in enumerate(stories_list))}

## 验收标准
{chr(10).join(f"- {criteria}" for criteria in criteria_list)}

## 功能范围
- 核心功能实现
- 错误处理
- 用户体验优化

## 边界条件
- 输入验证
- 权限控制
- 性能要求

## 项目约束
{chr(10).join(f"- {constraint}" for constraint in context.constraints)}

---
*生成时间: {datetime.now().isoformat()}*
"""


def _generate_design_spec(feature_name: str, description: str, context: ProjectContext) -> str:
    """生成设计规约"""
    return f"""# 设计规约 - {feature_name}

## 技术设计方案
基于项目架构原则和技术栈，为 {feature_name} 设计如下技术方案：

## 组件架构
```mermaid
graph LR
    A[前端组件] --> B[API接口]
    B --> C[业务逻辑]
    C --> D[数据层]
```

## API 设计
### 端点定义
- GET /api/{feature_name.lower()}
- POST /api/{feature_name.lower()}
- PUT /api/{feature_name.lower()}/{{id}}
- DELETE /api/{feature_name.lower()}/{{id}}

## 数据模型
```typescript
interface {feature_name.replace(' ', '')} {{
  id: string;
  name: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
}}
```

## 技术栈应用
{json.dumps(context.tech_stack, ensure_ascii=False, indent=2)}

## 设计原则遵循
{chr(10).join(f"- {principle}" for principle in context.design_principles)}

---
*生成时间: {datetime.now().isoformat()}*
"""


def _generate_tasks_spec(feature_name: str, requirements: str, design: str) -> str:
    """生成任务规约"""
    return f"""# 任务规约 - {feature_name}

## 开发任务列表

### 阶段 1: 基础设施
- [ ] 创建数据模型和数据库迁移
- [ ] 设置API路由和基础控制器
- [ ] 实现基础的CRUD操作

### 阶段 2: 核心功能
- [ ] 实现业务逻辑层
- [ ] 添加输入验证和错误处理
- [ ] 实现权限控制

### 阶段 3: 前端实现
- [ ] 创建前端组件
- [ ] 实现用户界面
- [ ] 集成API调用

### 阶段 4: 测试和优化
- [ ] 编写单元测试
- [ ] 编写集成测试
- [ ] 性能优化和代码审查

## 任务依赖关系
每个任务都可以追溯到需求规约中的具体验收标准。

## 完成标准
- 所有测试通过
- 代码审查完成
- 文档更新完成

---
*生成时间: {datetime.now().isoformat()}*
"""


@mcp.tool
def get_project_context() -> str:
    """获取当前项目的上下文信息"""
    context = load_project_context()
    if not context:
        return "❌ 项目尚未初始化，请先运行 initialize_project_constitution"

    return f"""📋 项目上下文信息

**项目名称**: {context.name}
**项目描述**: {context.description}

**技术栈**:
{json.dumps(context.tech_stack, ensure_ascii=False, indent=2)}

**团队信息**:
- 规模: {context.team_info['size']} 人
- 经验: {context.team_info['experience']}

**设计原则**:
{chr(10).join(f"- {principle}" for principle in context.design_principles)}

**约束条件**:
{chr(10).join(f"- {constraint}" for constraint in context.constraints)}

**创建时间**: {context.created_at}
**更新时间**: {context.updated_at}
"""


@mcp.tool
def list_features() -> str:
    """列出所有已创建的功能规约"""
    if not FEATURES_DIR.exists():
        return "📁 尚未创建任何功能规约"

    features = []
    for feature_dir in FEATURES_DIR.iterdir():
        if feature_dir.is_dir():
            feature_info = {
                "name": feature_dir.name,
                "has_requirements": (feature_dir / "requirements.md").exists(),
                "has_design": (feature_dir / "design.md").exists(),
                "has_tasks": (feature_dir / "tasks.md").exists()
            }
            features.append(feature_info)

    if not features:
        return "📁 尚未创建任何功能规约"

    result = "📋 功能规约列表:\n\n"
    for feature in features:
        status_icons = []
        if feature["has_requirements"]: status_icons.append("📝")
        if feature["has_design"]: status_icons.append("🏗️")
        if feature["has_tasks"]: status_icons.append("✅")

        result += f"- **{feature['name']}** {' '.join(status_icons)}\n"

    return result


@mcp.tool
def validate_code_compliance(file_path: str, code_content: str) -> str:
    """
    验证代码是否符合项目规约

    Args:
        file_path: 文件路径
        code_content: 代码内容
    """
    context = load_project_context()
    if not context:
        return "❌ 项目尚未初始化"

    issues = []

    # 基础代码质量检查
    if len(code_content.split('\n')) > 100:
        issues.append("⚠️ 文件过长，建议拆分为更小的模块")

    if 'TODO' in code_content or 'FIXME' in code_content:
        issues.append("⚠️ 代码中包含 TODO 或 FIXME 注释")

    # 检查是否符合约束条件
    for constraint in context.constraints:
        if 'ORM' in constraint and 'ORM' in code_content.upper():
            issues.append(f"❌ 违反约束条件: {constraint}")

    # 检查命名规范
    if file_path.endswith('.py'):
        if not file_path.islower():
            issues.append("⚠️ Python 文件名应使用小写字母和下划线")

    if not issues:
        return "✅ 代码符合项目规约"

    return f"📋 代码规约检查结果:\n\n" + "\n".join(issues)


@mcp.tool
def generate_tests(feature_name: str, test_type: str = "unit") -> str:
    """
    基于需求规约生成测试代码

    Args:
        feature_name: 功能名称
        test_type: 测试类型 (unit, integration, e2e)
    """
    feature_dir = FEATURES_DIR / feature_name.lower().replace(' ', '_')
    requirements_file = feature_dir / "requirements.md"

    if not requirements_file.exists():
        return f"❌ 功能 {feature_name} 的需求规约不存在"

    # 读取需求规约
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements_content = f.read()

    # 生成测试代码
    test_content = f"""# {test_type.title()} Tests for {feature_name}

import pytest
from unittest.mock import Mock, patch


class Test{feature_name.replace(' ', '')}:
    \"\"\"
    基于需求规约生成的测试用例
    \"\"\"

    def setup_method(self):
        \"\"\"测试前置设置\"\"\"
        pass

    def test_basic_functionality(self):
        \"\"\"测试基础功能\"\"\"
        # 基于验收标准生成的测试
        assert True  # 替换为实际测试逻辑

    def test_input_validation(self):
        \"\"\"测试输入验证\"\"\"
        # 测试边界条件和错误处理
        assert True  # 替换为实际测试逻辑

    def test_error_handling(self):
        \"\"\"测试错误处理\"\"\"
        # 测试异常情况
        assert True  # 替换为实际测试逻辑


# 集成测试示例
@pytest.mark.integration
def test_{feature_name.lower().replace(' ', '_')}_integration():
    \"\"\"集成测试\"\"\"
    pass


# 端到端测试示例
@pytest.mark.e2e
def test_{feature_name.lower().replace(' ', '_')}_e2e():
    \"\"\"端到端测试\"\"\"
    pass
"""

    # 保存测试文件
    test_file = feature_dir / f"test_{feature_name.lower().replace(' ', '_')}.py"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)

    return f"✅ 已生成 {test_type} 测试文件: {test_file}"


@mcp.tool
def sync_documentation(feature_name: str) -> str:
    """
    同步功能文档，确保文档与代码保持一致

    Args:
        feature_name: 功能名称
    """
    feature_dir = FEATURES_DIR / feature_name.lower().replace(' ', '_')

    if not feature_dir.exists():
        return f"❌ 功能 {feature_name} 不存在"

    # 检查文档完整性
    docs_status = {
        "requirements.md": (feature_dir / "requirements.md").exists(),
        "design.md": (feature_dir / "design.md").exists(),
        "tasks.md": (feature_dir / "tasks.md").exists()
    }

    missing_docs = [doc for doc, exists in docs_status.items() if not exists]

    if missing_docs:
        return f"⚠️ 缺少文档文件: {', '.join(missing_docs)}"

    # 更新主 README
    readme_content = f"""# {feature_name} 功能文档

## 文档结构
- [需求规约](requirements.md) - 功能需求和验收标准
- [设计规约](design.md) - 技术设计和架构方案
- [任务规约](tasks.md) - 开发任务和执行计划

## 最后更新
{datetime.now().isoformat()}

---
*此文档由规约驱动开发框架自动生成*
"""

    with open(feature_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)

    return f"✅ 已同步 {feature_name} 功能文档"


@mcp.tool
def update_project_context(
    field: str,
    value: str
) -> str:
    """
    更新项目上下文信息

    Args:
        field: 要更新的字段 (description, tech_stack, design_principles, constraints)
        value: 新值
    """
    context = load_project_context()
    if not context:
        return "❌ 项目尚未初始化"

    try:
        if field == "description":
            context.description = value
        elif field == "tech_stack":
            context.tech_stack = json.loads(value) if value.startswith('{') else {"description": value}
        elif field == "design_principles":
            context.design_principles = [p.strip() for p in value.split(',')]
        elif field == "constraints":
            context.constraints = [c.strip() for c in value.split(',')]
        else:
            return f"❌ 不支持的字段: {field}"

        context.updated_at = datetime.now().isoformat()
        save_project_context(context)

        return f"✅ 已更新项目上下文字段: {field}"

    except Exception as e:
        return f"❌ 更新失败: {str(e)}"


if __name__ == "__main__":
    # 运行 MCP 服务器
    mcp.run()
