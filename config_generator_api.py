#!/usr/bin/env python3
"""
MCP配置生成器API
为不同AI工具生成MCP配置文件
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import PlainTextResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import json
import yaml
import platform

app = FastAPI(
    title="MCP配置生成器",
    description="为各种AI工具生成MCP桥接器配置",
    version="1.0.0"
)

# 允许跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ConfigRequest(BaseModel):
    """配置请求模型"""
    ai_tool: str  # claude, cursor, cline, continue
    server_url: str
    token: str
    config_type: str = "online"  # online, local, pypi
    custom_tools: Optional[Dict[str, Any]] = None

@app.get("/")
def read_root():
    """主页"""
    return {
        "message": "MCP配置生成器",
        "supported_tools": ["claude", "cursor", "cline", "continue"],
        "endpoints": {
            "generate": "/config/generate",
            "bridge": "/bridge/universal.py",
            "setup": "/setup/{tool}"
        }
    }

@app.post("/config/generate")
def generate_config(request: ConfigRequest):
    """生成配置文件"""
    
    generators = {
        "claude": generate_claude_config,
        "cursor": generate_cursor_config,
        "cline": generate_cline_config,
        "continue": generate_continue_config
    }
    
    if request.ai_tool not in generators:
        raise HTTPException(status_code=400, detail=f"不支持的AI工具: {request.ai_tool}")
    
    try:
        config = generators[request.ai_tool](request)
        return {
            "success": True,
            "ai_tool": request.ai_tool,
            "config": config,
            "instructions": get_installation_instructions(request.ai_tool)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def generate_claude_config(request: ConfigRequest) -> Dict[str, Any]:
    """生成Claude Desktop配置"""
    
    if request.config_type == "online":
        args = [
            "-c",
            f"import urllib.request; exec(urllib.request.urlopen('{request.server_url}/bridge/universal.py').read())"
        ]
    elif request.config_type == "local":
        args = ["universal_mcp_bridge.py"]
    else:  # pypi
        args = ["spec-mcp-bridge"]
    
    config = {
        "mcpServers": {
            "spec-driven-development": {
                "command": "python",
                "args": args,
                "env": {
                    "MCP_SERVER_URL": request.server_url,
                    "MCP_TOKEN": request.token
                }
            }
        }
    }
    
    return config

def generate_cursor_config(request: ConfigRequest) -> Dict[str, Any]:
    """生成Cursor配置"""
    
    if request.config_type == "online":
        args = [
            "-c",
            f"exec(__import__('urllib.request').urlopen('{request.server_url}/bridge/universal.py').read())"
        ]
    else:
        args = [
            "universal_mcp_bridge.py",
            "--server-url", request.server_url,
            "--token", request.token
        ]
    
    config = {
        "mcpServers": {
            "spec-driven-development": {
                "command": "python",
                "args": args,
                "env": {
                    "MCP_SERVER_URL": request.server_url,
                    "MCP_TOKEN": request.token
                } if request.config_type == "online" else {}
            }
        }
    }
    
    return config

def generate_cline_config(request: ConfigRequest) -> Dict[str, Any]:
    """生成Cline (VSCode) 配置"""
    
    config = {
        "cline.mcpServers": {
            "spec-driven-development": {
                "command": "python",
                "args": [
                    "-c",
                    f"exec(__import__('urllib.request').urlopen('{request.server_url}/bridge/universal.py').read())"
                ],
                "env": {
                    "MCP_SERVER_URL": request.server_url,
                    "MCP_TOKEN": request.token
                }
            }
        }
    }
    
    return config

def generate_continue_config(request: ConfigRequest) -> Dict[str, Any]:
    """生成Continue配置"""
    
    config = {
        "models": ["请保留您现有的模型配置"],
        "mcpServers": [
            {
                "name": "spec-driven-development",
                "command": "python",
                "args": [
                    "-c",
                    f"exec(__import__('urllib.request').urlopen('{request.server_url}/bridge/universal.py').read())"
                ],
                "env": {
                    "MCP_SERVER_URL": request.server_url,
                    "MCP_TOKEN": request.token
                }
            }
        ]
    }
    
    return config

def get_installation_instructions(ai_tool: str) -> Dict[str, Any]:
    """获取安装说明"""
    
    instructions = {
        "claude": {
            "config_path": {
                "windows": "%APPDATA%/Claude/claude_desktop_config.json",
                "macos": "~/Library/Application Support/Claude/claude_desktop_config.json",
                "linux": "~/.config/claude/claude_desktop_config.json"
            },
            "steps": [
                "1. 找到Claude Desktop配置文件",
                "2. 备份现有配置（如果有）",
                "3. 将生成的配置合并到现有配置中",
                "4. 重启Claude Desktop",
                "5. 在聊天中输入'列出可用工具'测试连接"
            ]
        },
        "cursor": {
            "config_path": {
                "windows": "%APPDATA%/Cursor/User/globalStorage/cursor.mcp/config.json",
                "macos": "~/Library/Application Support/Cursor/User/globalStorage/cursor.mcp/config.json",
                "linux": "~/.config/Cursor/User/globalStorage/cursor.mcp/config.json"
            },
            "steps": [
                "1. 创建Cursor MCP配置目录（如果不存在）",
                "2. 将配置保存到config.json文件",
                "3. 重启Cursor",
                "4. 在Cursor聊天中测试MCP工具"
            ]
        },
        "cline": {
            "config_path": {
                "all": "VSCode Settings (settings.json)"
            },
            "steps": [
                "1. 打开VSCode设置 (Ctrl/Cmd + ,)",
                "2. 点击右上角的'打开设置(JSON)'图标",
                "3. 将配置添加到settings.json",
                "4. 重新加载VSCode窗口",
                "5. 在Cline扩展中测试工具"
            ]
        },
        "continue": {
            "config_path": {
                "all": "~/.continue/config.json"
            },
            "steps": [
                "1. 找到Continue配置文件",
                "2. 备份现有配置",
                "3. 将mcpServers部分添加到配置中",
                "4. 重启VSCode",
                "5. 在Continue扩展中测试"
            ]
        }
    }
    
    return instructions.get(ai_tool, {})

@app.get("/bridge/universal.py", response_class=PlainTextResponse)
def get_bridge_script():
    """返回通用桥接器脚本"""
    
    # 读取通用桥接器脚本内容
    try:
        with open("universal_mcp_bridge.py", "r", encoding="utf-8") as f:
            script_content = f.read()
        return script_content
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="桥接器脚本未找到")

@app.get("/setup/{tool}")
def get_setup_script(tool: str, token: str = Query(...), server_url: str = Query(None)):
    """生成一键安装脚本"""
    
    if server_url is None:
        server_url = "https://your-domain.com"  # 使用当前域名
    
    supported_tools = ["claude", "cursor", "cline", "continue", "all"]
    if tool not in supported_tools:
        raise HTTPException(status_code=400, detail=f"不支持的工具: {tool}")
    
    if tool == "all":
        script = generate_all_setup_script(token, server_url)
    else:
        script = generate_single_setup_script(tool, token, server_url)
    
    return PlainTextResponse(script, media_type="text/plain")

def generate_single_setup_script(tool: str, token: str, server_url: str) -> str:
    """生成单个工具的安装脚本"""
    
    scripts = {
        "claude": f'''#!/bin/bash
# Claude Desktop MCP 配置脚本

echo "🚀 配置Claude Desktop MCP..."

# 检测操作系统
OS=$(uname -s)
if [[ "$OS" == "Darwin" ]]; then
    CONFIG_PATH="$HOME/Library/Application Support/Claude/claude_desktop_config.json"
elif [[ "$OS" == "Linux" ]]; then
    CONFIG_PATH="$HOME/.config/claude/claude_desktop_config.json"
else
    echo "❌ 不支持的操作系统: $OS"
    exit 1
fi

# 创建配置目录
mkdir -p "$(dirname "$CONFIG_PATH")"

# 生成配置
cat > "$CONFIG_PATH" << 'EOF'
{{
  "mcpServers": {{
    "spec-driven-development": {{
      "command": "python",
      "args": [
        "-c",
        "import urllib.request; exec(urllib.request.urlopen('{server_url}/bridge/universal.py').read())"
      ],
      "env": {{
        "MCP_SERVER_URL": "{server_url}",
        "MCP_TOKEN": "{token}"
      }}
    }}
  }}
}}
EOF

echo "✅ Claude Desktop配置完成！"
echo "📁 配置文件: $CONFIG_PATH"
echo "🔄 请重启Claude Desktop以使配置生效"
''',
        
        "cursor": f'''#!/bin/bash
# Cursor MCP 配置脚本

echo "🚀 配置Cursor MCP..."

# 检测操作系统和配置路径
OS=$(uname -s)
if [[ "$OS" == "Darwin" ]]; then
    CONFIG_PATH="$HOME/Library/Application Support/Cursor/User/globalStorage/cursor.mcp/config.json"
elif [[ "$OS" == "Linux" ]]; then
    CONFIG_PATH="$HOME/.config/Cursor/User/globalStorage/cursor.mcp/config.json"
else
    echo "❌ 不支持的操作系统: $OS"
    exit 1
fi

# 创建配置目录
mkdir -p "$(dirname "$CONFIG_PATH")"

# 生成配置
cat > "$CONFIG_PATH" << 'EOF'
{{
  "mcpServers": {{
    "spec-driven-development": {{
      "command": "python",
      "args": [
        "-c",
        "exec(__import__('urllib.request').urlopen('{server_url}/bridge/universal.py').read())"
      ],
      "env": {{
        "MCP_SERVER_URL": "{server_url}",
        "MCP_TOKEN": "{token}"
      }}
    }}
  }}
}}
EOF

echo "✅ Cursor配置完成！"
echo "📁 配置文件: $CONFIG_PATH"
echo "🔄 请重启Cursor以使配置生效"
'''
    }
    
    return scripts.get(tool, "# 不支持的工具")

def generate_all_setup_script(token: str, server_url: str) -> str:
    """生成配置所有工具的脚本"""
    
    return f'''#!/bin/bash
# 通用MCP配置脚本 - 配置所有支持的AI工具

echo "🚀 开始配置所有支持的AI工具..."
echo "服务器: {server_url}"
echo "Token: {token[:10]}..."

# 检测操作系统
OS=$(uname -s)
echo "操作系统: $OS"

# 配置Claude Desktop
configure_claude() {{
    echo "🎯 配置Claude Desktop..."
    
    if [[ "$OS" == "Darwin" ]]; then
        CONFIG_PATH="$HOME/Library/Application Support/Claude/claude_desktop_config.json"
    elif [[ "$OS" == "Linux" ]]; then
        CONFIG_PATH="$HOME/.config/claude/claude_desktop_config.json"
    else
        echo "⚠️  跳过Claude Desktop (不支持的OS)"
        return
    fi
    
    if [[ -d "$(dirname "$CONFIG_PATH")" ]] || [[ -f "$CONFIG_PATH" ]]; then
        mkdir -p "$(dirname "$CONFIG_PATH")"
        
        cat > "$CONFIG_PATH" << 'EOF'
{{
  "mcpServers": {{
    "spec-driven-development": {{
      "command": "python",
      "args": [
        "-c",
        "import urllib.request; exec(urllib.request.urlopen('{server_url}/bridge/universal.py').read())"
      ],
      "env": {{
        "MCP_SERVER_URL": "{server_url}",
        "MCP_TOKEN": "{token}"
      }}
    }}
  }}
}}
EOF
        echo "✅ Claude Desktop配置完成"
    else
        echo "⚠️  未检测到Claude Desktop"
    fi
}}

# 配置Cursor
configure_cursor() {{
    echo "🎯 配置Cursor..."
    
    if [[ "$OS" == "Darwin" ]]; then
        CONFIG_PATH="$HOME/Library/Application Support/Cursor/User/globalStorage/cursor.mcp/config.json"
    elif [[ "$OS" == "Linux" ]]; then
        CONFIG_PATH="$HOME/.config/Cursor/User/globalStorage/cursor.mcp/config.json"
    else
        echo "⚠️  跳过Cursor (不支持的OS)"
        return
    fi
    
    # 检查Cursor是否安装
    if command -v cursor &> /dev/null || [[ -d "$(dirname "$(dirname "$(dirname "$CONFIG_PATH")")")" ]]; then
        mkdir -p "$(dirname "$CONFIG_PATH")"
        
        cat > "$CONFIG_PATH" << 'EOF'
{{
  "mcpServers": {{
    "spec-driven-development": {{
      "command": "python",
      "args": [
        "-c",
        "exec(__import__('urllib.request').urlopen('{server_url}/bridge/universal.py').read())"
      ],
      "env": {{
        "MCP_SERVER_URL": "{server_url}",
        "MCP_TOKEN": "{token}"
      }}
    }}
  }}
}}
EOF
        echo "✅ Cursor配置完成"
    else
        echo "⚠️  未检测到Cursor"
    fi
}}

# 执行配置
configure_claude
configure_cursor

echo ""
echo "🎉 配置完成！"
echo "📋 请重启相应的AI工具以使配置生效"
echo "🔧 如有问题，请访问: {server_url}/docs"
'''

@app.get("/api/v1/tools/mappings")
def get_tools_mappings():
    """返回工具映射配置"""
    
    # 这里返回标准的工具映射
    # 实际使用时应该从数据库或配置文件读取
    return {
        'initialize_project_constitution': {
            'endpoint': '/api/v1/projects/init',
            'method': 'POST',
            'description': '初始化项目宪法，创建项目的核心规约文档',
            'parameters': {
                'project_name': {'type': 'string', 'description': '项目名称', 'required': True}
                # ... 其他参数
            }
        },
        # ... 其他工具映射
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001) 