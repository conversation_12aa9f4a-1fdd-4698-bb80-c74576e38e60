#!/usr/bin/env python3
"""
规约驱动开发 MCP 服务器启动脚本
"""

import sys
import subprocess
import os
from pathlib import Path


def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import fastmcp
        import pydantic
        print("✅ 依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False


def create_project_structure():
    """创建项目目录结构"""
    spec_dir = Path(".project-spec")
    features_dir = spec_dir / "features"
    
    spec_dir.mkdir(exist_ok=True)
    features_dir.mkdir(exist_ok=True)
    
    print("✅ 项目目录结构已创建")


def main():
    """主函数"""
    print("🚀 启动规约驱动开发 MCP 服务器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建项目结构
    create_project_structure()
    
    # 显示使用说明
    print("\n📋 使用说明:")
    print("1. 首先调用 initialize_project_constitution 初始化项目")
    print("2. 使用 generate_feature_spec 生成功能规约")
    print("3. 使用其他工具进行开发和验证")
    print("\n💡 提示: 查看 example_usage.py 了解完整使用流程")
    print("\n🔧 MCP 配置文件: mcp_config.json")
    print("=" * 50)
    
    # 启动服务器
    try:
        print("\n🌟 MCP 服务器正在启动...")
        from spec_driven_mcp import mcp
        mcp.run()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
