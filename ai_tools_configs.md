# 各AI工具MCP配置指南

## 🎯 支持的AI工具

### 1. <PERSON>

#### 配置文件位置
- **Windows**: `%APPDATA%/Claude/claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/claude/claude_desktop_config.json`

#### 配置示例
```json
{
  "mcpServers": {
    "spec-driven-development": {
      "command": "python",
      "args": ["universal_mcp_bridge.py"],
      "env": {
        "MCP_SERVER_URL": "https://your-domain.com",
        "MCP_TOKEN": "your-api-token"
      }
    }
  }
}
```

#### 在线启动配置
```json
{
  "mcpServers": {
    "spec-driven-development": {
      "command": "python",
      "args": [
        "-c",
        "import urllib.request; exec(urllib.request.urlopen('https://your-domain.com/bridge/universal.py').read())"
      ],
      "env": {
        "MCP_SERVER_URL": "https://your-domain.com",
        "MCP_TOKEN": "your-api-token"
      }
    }
  }
}
```

### 2. Cursor

#### 配置文件位置
- **Windows**: `%APPDATA%/Cursor/User/globalStorage/cursor.mcp/config.json`
- **macOS**: `~/Library/Application Support/Cursor/User/globalStorage/cursor.mcp/config.json`
- **Linux**: `~/.config/Cursor/User/globalStorage/cursor.mcp/config.json`

#### 配置示例
```json
{
  "mcpServers": {
    "spec-driven-development": {
      "command": "python",
      "args": [
        "universal_mcp_bridge.py",
        "--server-url", "https://your-domain.com",
        "--token", "your-api-token"
      ]
    }
  }
}
```

#### 简化配置（推荐）
```json
{
  "mcpServers": {
    "spec-driven-development": {
      "command": "curl",
      "args": [
        "-s", "https://your-domain.com/bridge/run.py",
        "|", "python", "-",
        "--server", "https://your-domain.com",
        "--token", "your-api-token"
      ]
    }
  }
}
```

### 3. Cline (VSCode扩展)

#### 配置方式
通过VSCode设置配置：

```json
{
  "cline.mcpServers": {
    "spec-driven-development": {
      "command": "python",
      "args": [
        "-c",
        "exec(__import__('urllib.request').urlopen('https://your-domain.com/bridge/universal.py').read())"
      ],
      "env": {
        "MCP_SERVER_URL": "https://your-domain.com",
        "MCP_TOKEN": "your-api-token"
      }
    }
  }
}
```

#### 或通过命令行
```bash
# 设置Cline MCP配置
code --install-extension cline
# 然后在settings.json中添加上述配置
```

### 4. Continue (VSCode扩展)

#### 配置文件位置
`~/.continue/config.json`

#### 配置示例
```json
{
  "models": [...],
  "mcpServers": [
    {
      "name": "spec-driven-development",
      "command": "python",
      "args": [
        "universal_mcp_bridge.py"
      ],
      "env": {
        "MCP_SERVER_URL": "https://your-domain.com",
        "MCP_TOKEN": "your-api-token"
      }
    }
  ]
}
```

### 5. 其他MCP兼容工具

#### 通用配置模板
```json
{
  "mcpServers": {
    "spec-driven-development": {
      "command": "python",
      "args": ["universal_mcp_bridge.py"],
      "env": {
        "MCP_SERVER_URL": "https://your-domain.com",
        "MCP_TOKEN": "your-api-token",
        "MCP_CONFIG": "./mcp_bridge_config.yaml"
      }
    }
  }
}
```

## 🔧 配置方法对比

| 方法 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| 本地脚本 | 快速启动，离线可用 | 需要下载文件 | 开发环境 |
| 在线执行 | 零下载，自动更新 | 需要网络连接 | 生产使用 |
| PyPI包 | 标准化安装 | 需要pip install | 长期使用 |
| 配置文件 | 灵活配置 | 配置复杂 | 高级用户 |

## 🚀 一键配置脚本

### 自动检测和配置
```bash
#!/bin/bash
# auto_configure.sh - 自动配置MCP桥接器

echo "🔍 检测AI工具..."

# 检测Claude Desktop
if [ -d "$HOME/Library/Application Support/Claude" ] || [ -d "$APPDATA/Claude" ]; then
    echo "✅ 发现Claude Desktop"
    CLAUDE_CONFIG="$HOME/Library/Application Support/Claude/claude_desktop_config.json"
    if [ "$OS" = "Windows_NT" ]; then
        CLAUDE_CONFIG="$APPDATA/Claude/claude_desktop_config.json"
    fi
    configure_claude "$CLAUDE_CONFIG"
fi

# 检测Cursor
if [ -d "$HOME/Library/Application Support/Cursor" ] || [ -d "$APPDATA/Cursor" ]; then
    echo "✅ 发现Cursor"
    CURSOR_CONFIG="$HOME/Library/Application Support/Cursor/User/globalStorage/cursor.mcp/config.json"
    if [ "$OS" = "Windows_NT" ]; then
        CURSOR_CONFIG="$APPDATA/Cursor/User/globalStorage/cursor.mcp/config.json"
    fi
    configure_cursor "$CURSOR_CONFIG"
fi

# 检测VSCode + Cline
if command -v code &> /dev/null; then
    if code --list-extensions | grep -q "cline"; then
        echo "✅ 发现Cline (VSCode)"
        configure_cline
    fi
    
    if code --list-extensions | grep -q "continue"; then
        echo "✅ 发现Continue (VSCode)"
        configure_continue
    fi
fi

echo "🎉 配置完成！请重启相应的AI工具。"
```

### Windows PowerShell版本
```powershell
# auto_configure.ps1
Write-Host "🔍 检测AI工具..." -ForegroundColor Green

# 检测Claude Desktop
$claudePath = "$env:APPDATA\Claude"
if (Test-Path $claudePath) {
    Write-Host "✅ 发现Claude Desktop" -ForegroundColor Green
    Configure-Claude
}

# 检测Cursor
$cursorPath = "$env:APPDATA\Cursor"
if (Test-Path $cursorPath) {
    Write-Host "✅ 发现Cursor" -ForegroundColor Green
    Configure-Cursor
}

Write-Host "🎉 配置完成！" -ForegroundColor Green
```

## 🎯 使用流程

### 方式1: 手动配置
1. 选择你的AI工具
2. 找到对应的配置文件位置
3. 复制相应的配置内容
4. 替换服务器地址和Token
5. 重启AI工具

### 方式2: 自动配置
```bash
# 一键配置所有支持的AI工具
curl -sSL https://your-domain.com/setup/all | bash

# 或仅配置特定工具
curl -sSL https://your-domain.com/setup/claude | bash
curl -sSL https://your-domain.com/setup/cursor | bash
```

### 方式3: 配置生成器
访问 `https://your-domain.com/config-generator` 生成个性化配置：

1. 选择AI工具
2. 输入服务器地址
3. 输入API Token
4. 下载配置文件
5. 放置到对应位置

## 🔧 配置验证

### 测试连接
```bash
# 测试MCP连接
python universal_mcp_bridge.py --test-connection \
  --server-url https://your-domain.com \
  --token your-api-token
```

### 验证工具列表
```bash
# 验证工具是否正确加载
python universal_mcp_bridge.py --list-tools \
  --server-url https://your-domain.com \
  --token your-api-token
```

## 🆘 故障排除

### 常见问题

1. **认证失败**
   - 检查Token是否正确
   - 确认服务器地址可访问
   
2. **工具不显示**
   - 检查网络连接
   - 验证工具映射配置
   
3. **配置文件不生效**
   - 确认文件路径正确
   - 重启AI工具
   
4. **Python依赖错误**
   - 安装required包: `pip install httpx pyyaml`

### 调试模式
```bash
# 启用调试模式
export MCP_DEBUG=true
python universal_mcp_bridge.py
```

## 📞 技术支持

- 📖 文档: https://your-domain.com/docs
- 🐛 问题反馈: https://github.com/your-repo/issues
- 💬 社区讨论: https://discord.gg/your-community 