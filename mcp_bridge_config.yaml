# Universal MCP Bridge Configuration
# 通用MCP桥接器配置文件

# 服务器配置
server:
  url: "https://your-domain.com"  # 你的MCP服务器地址
  timeout: 30.0                  # 请求超时时间（秒）
  retry_count: 3                 # 重试次数

# 认证配置
auth:
  type: "bearer"                 # 认证类型: bearer, api_key, basic, none
  token: "your-api-token"        # 认证令牌
  header_name: "Authorization"   # 认证头名称
  prefix: "Bearer"               # 令牌前缀

# 工具映射配置（可选，如果不配置会从服务器动态获取）
tools:
  # 项目管理工具
  initialize_project_constitution:
    endpoint: "/api/v1/projects/init"
    method: "POST"
    description: "初始化项目宪法，创建项目的核心规约文档"
    parameters:
      project_name:
        type: "string"
        description: "项目名称"
        required: true
      project_description:
        type: "string"
        description: "项目描述"
        required: true
      tech_stack:
        type: "string"
        description: "技术栈描述（JSON格式）"
        required: true
      team_size:
        type: "integer"
        description: "团队规模"
        required: true
      team_experience:
        type: "string"
        description: "团队经验水平"
        required: true
      performance_requirements:
        type: "string"
        description: "性能需求"
        required: true
      security_requirements:
        type: "string"
        description: "安全需求"
        required: true
      design_principles:
        type: "string"
        description: "设计原则（逗号分隔）"
        required: true
      constraints:
        type: "string"
        description: "约束条件（逗号分隔）"
        required: true

  get_project_context:
    endpoint: "/api/v1/projects/context"
    method: "GET"
    description: "获取当前项目的上下文信息"
    parameters: {}

  update_project_context:
    endpoint: "/api/v1/projects/context"
    method: "PUT"
    description: "更新项目上下文信息"
    parameters:
      field:
        type: "string"
        description: "要更新的字段"
        required: true
      value:
        type: "string"
        description: "新值"
        required: true

  # 功能开发工具
  generate_feature_spec:
    endpoint: "/api/v1/features/generate"
    method: "POST"
    description: "生成功能规约，包括需求、设计和任务规约"
    parameters:
      feature_name:
        type: "string"
        description: "功能名称"
        required: true
      feature_description:
        type: "string"
        description: "功能描述"
        required: true
      user_stories:
        type: "string"
        description: "用户故事（每行一个）"
        required: true
      acceptance_criteria:
        type: "string"
        description: "验收标准（每行一个）"
        required: true

  list_features:
    endpoint: "/api/v1/features"
    method: "GET"
    description: "列出所有已创建的功能规约"
    parameters: {}

  # 质量保障工具
  validate_code_compliance:
    endpoint: "/api/v1/validate/code"
    method: "POST"
    description: "验证代码是否符合项目规约"
    parameters:
      file_path:
        type: "string"
        description: "文件路径"
        required: true
      code_content:
        type: "string"
        description: "代码内容"
        required: true

  generate_tests:
    endpoint: "/api/v1/tests/generate"
    method: "POST"
    description: "基于需求规约生成测试代码"
    parameters:
      feature_name:
        type: "string"
        description: "功能名称"
        required: true
      test_type:
        type: "string"
        description: "测试类型（unit, integration, e2e）"
        default: "unit"

  sync_documentation:
    endpoint: "/api/v1/docs/sync"
    method: "POST"
    description: "同步功能文档，确保文档与代码保持一致"
    parameters:
      feature_name:
        type: "string"
        description: "功能名称"
        required: true

# 日志配置
logging:
  level: "INFO"                  # 日志级别: DEBUG, INFO, WARNING, ERROR
  file: null                     # 日志文件路径，null表示输出到控制台

# 扩展配置
extensions:
  # 自定义工具加载器
  custom_tools: []
  
  # 插件配置
  plugins: []
  
  # 缓存配置
  cache:
    enabled: false
    ttl: 300  # 缓存时间（秒） 